# Android Keystore Generation Script
# This script generates a keystore for signing Android APK files

param(
    [string]$KeystoreName = "android-release-key.jks",
    [string]$KeyAlias = "release-key",
    [string]$OutputPath = "",
    [int]$ValidityYears = 25,
    [string]$KeySize = "2048",
    [string]$Algorithm = "RSA"
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Get script directory
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectRoot = Split-Path -Parent $ScriptDir

# Set default output path if not provided
if ([string]::IsNullOrEmpty($OutputPath)) {
    $OutputPath = Join-Path $ProjectRoot "certificates"
}

# Ensure output directory exists
if (-not (Test-Path $OutputPath)) {
    New-Item -ItemType Directory -Path $OutputPath -Force | Out-Null
    Write-Host "Created certificates directory: $OutputPath" -ForegroundColor Yellow
}

$KeystorePath = Join-Path $OutputPath $KeystoreName

Write-Host "=== Android Keystore Generation ===" -ForegroundColor Green
Write-Host "Keystore Path: $KeystorePath" -ForegroundColor Cyan
Write-Host "Key Alias: $KeyAlias" -ForegroundColor Cyan
Write-Host "Validity: $ValidityYears years" -ForegroundColor Cyan

# Check if keystore already exists
if (Test-Path $KeystorePath) {
    Write-Warning "Keystore already exists: $KeystorePath"
    $overwrite = Read-Host "Do you want to overwrite it? (y/N)"
    if ($overwrite -ne "y" -and $overwrite -ne "Y") {
        Write-Host "Operation cancelled" -ForegroundColor Yellow
        exit 0
    }
    Remove-Item $KeystorePath -Force
}

# Check if keytool is available
try {
    $keytoolVersion = keytool -help 2>&1 | Select-Object -First 1
    Write-Host "Using keytool from Java installation" -ForegroundColor Green
}
catch {
    Write-Error "keytool not found. Please ensure Java JDK is installed and in PATH."
    Write-Host "Download Java JDK from: https://adoptium.net/" -ForegroundColor Yellow
    exit 1
}

# Collect certificate information
Write-Host "`nPlease provide certificate information:" -ForegroundColor Yellow

$firstName = Read-Host "First and Last Name (CN)"
if ([string]::IsNullOrEmpty($firstName)) {
    $firstName = "MAUI App Developer"
}

$orgUnit = Read-Host "Organizational Unit (OU)"
if ([string]::IsNullOrEmpty($orgUnit)) {
    $orgUnit = "Development Team"
}

$organization = Read-Host "Organization (O)"
if ([string]::IsNullOrEmpty($organization)) {
    $organization = "Your Company"
}

$city = Read-Host "City or Locality (L)"
if ([string]::IsNullOrEmpty($city)) {
    $city = "Your City"
}

$state = Read-Host "State or Province (ST)"
if ([string]::IsNullOrEmpty($state)) {
    $state = "Your State"
}

$country = Read-Host "Country Code (C) - 2 letters"
if ([string]::IsNullOrEmpty($country)) {
    $country = "US"
}

# Collect passwords
Write-Host "`nPassword Configuration:" -ForegroundColor Yellow
$keystorePassword = Read-Host "Keystore Password" -AsSecureString
$keyPassword = Read-Host "Key Password (press Enter to use same as keystore)" -AsSecureString

# Convert secure strings to plain text for keytool
$keystorePasswordPlain = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($keystorePassword))

if ($keyPassword.Length -eq 0) {
    $keyPasswordPlain = $keystorePasswordPlain
} else {
    $keyPasswordPlain = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($keyPassword))
}

# Build distinguished name
$distinguishedName = "CN=$firstName, OU=$orgUnit, O=$organization, L=$city, ST=$state, C=$country"

# Calculate validity in days
$validityDays = $ValidityYears * 365

# Generate keystore
Write-Host "`nGenerating keystore..." -ForegroundColor Yellow

$keytoolArgs = @(
    "-genkey"
    "-v"
    "-keystore", $KeystorePath
    "-keyalg", $Algorithm
    "-keysize", $KeySize
    "-validity", $validityDays
    "-alias", $KeyAlias
    "-dname", $distinguishedName
    "-storepass", $keystorePasswordPlain
    "-keypass", $keyPasswordPlain
)

try {
    $process = Start-Process -FilePath "keytool" -ArgumentList $keytoolArgs -Wait -PassThru -NoNewWindow
    
    if ($process.ExitCode -eq 0) {
        Write-Host "Keystore generated successfully!" -ForegroundColor Green
    } else {
        Write-Error "Keystore generation failed with exit code: $($process.ExitCode)"
        exit 1
    }
}
catch {
    Write-Error "Failed to generate keystore: $($_.Exception.Message)"
    exit 1
}

# Verify keystore
Write-Host "`nVerifying keystore..." -ForegroundColor Yellow
try {
    $verifyArgs = @(
        "-list"
        "-v"
        "-keystore", $KeystorePath
        "-storepass", $keystorePasswordPlain
    )
    
    keytool @verifyArgs
    Write-Host "Keystore verification completed" -ForegroundColor Green
}
catch {
    Write-Warning "Keystore verification failed: $($_.Exception.Message)"
}

# Generate configuration template
$configTemplate = @"
<!-- Add this to your MauiFrontEnd.csproj file -->
<PropertyGroup Condition="'`$(Configuration)' == 'Release'">
    <AndroidKeyStore>true</AndroidKeyStore>
    <AndroidSigningKeyStore>$KeystorePath</AndroidSigningKeyStore>
    <AndroidSigningKeyAlias>$KeyAlias</AndroidSigningKeyAlias>
    <AndroidSigningKeyPass>$keyPasswordPlain</AndroidSigningKeyPass>
    <AndroidSigningStorePass>$keystorePasswordPlain</AndroidSigningStorePass>
</PropertyGroup>
"@

$configPath = Join-Path $OutputPath "keystore-config.xml"
$configTemplate | Out-File -FilePath $configPath -Encoding UTF8

# Generate PowerShell configuration
$psConfigTemplate = @"
# PowerShell variables for build script
`$KeystorePath = "$KeystorePath"
`$KeyAlias = "$KeyAlias"
`$KeystorePassword = "$keystorePasswordPlain"
`$KeyPassword = "$keyPasswordPlain"

# Usage example:
# .\build-android-release.ps1 -KeystorePath `$KeystorePath -KeyAlias `$KeyAlias -KeystorePassword `$KeystorePassword -KeyPassword `$KeyPassword
"@

$psConfigPath = Join-Path $OutputPath "keystore-config.ps1"
$psConfigTemplate | Out-File -FilePath $psConfigPath -Encoding UTF8

# Clear sensitive variables
$keystorePasswordPlain = $null
$keyPasswordPlain = $null

Write-Host "`n=== Keystore Generation Complete ===" -ForegroundColor Green
Write-Host "Keystore file: $KeystorePath" -ForegroundColor Cyan
Write-Host "Configuration template: $configPath" -ForegroundColor Cyan
Write-Host "PowerShell config: $psConfigPath" -ForegroundColor Cyan

Write-Host "`n=== Security Recommendations ===" -ForegroundColor Yellow
Write-Host "1. Store keystore file in a secure location" -ForegroundColor White
Write-Host "2. Backup keystore file - you cannot regenerate it" -ForegroundColor White
Write-Host "3. Keep passwords secure and confidential" -ForegroundColor White
Write-Host "4. Do not commit keystore or passwords to version control" -ForegroundColor White
Write-Host "5. Consider using environment variables for passwords in CI/CD" -ForegroundColor White

Write-Host "`n=== Next Steps ===" -ForegroundColor Yellow
Write-Host "1. Update your project file with the signing configuration" -ForegroundColor White
Write-Host "2. Test building a signed APK using the build script" -ForegroundColor White
Write-Host "3. Store the keystore securely for future releases" -ForegroundColor White
