# Android Release Build Script for MAUI Push Notifications App
# This script builds a signed release APK for deployment to Samsung Galaxy S10+

param(
    [string]$Configuration = "Release",
    [string]$OutputPath = "",
    [switch]$Clean = $false,
    [switch]$Verbose = $false,
    [string]$KeystorePath = "",
    [string]$KeystorePassword = "",
    [string]$KeyAlias = "",
    [string]$KeyPassword = ""
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Get script directory and project root
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectRoot = Split-Path -Parent $ScriptDir
$ProjectFile = Join-Path $ProjectRoot "src\MauiFE\MauiFrontEnd.csproj"

Write-Host "=== MAUI Android Release Build Script ===" -ForegroundColor Green
Write-Host "Project Root: $ProjectRoot" -ForegroundColor Cyan
Write-Host "Configuration: $Configuration" -ForegroundColor Cyan

# Validate project file exists
if (-not (Test-Path $ProjectFile)) {
    Write-Error "Project file not found: $ProjectFile"
    exit 1
}

# Set default output path if not provided
if ([string]::IsNullOrEmpty($OutputPath)) {
    $OutputPath = Join-Path $ProjectRoot "artifacts\android"
}

# Ensure output directory exists
if (-not (Test-Path $OutputPath)) {
    New-Item -ItemType Directory -Path $OutputPath -Force | Out-Null
    Write-Host "Created output directory: $OutputPath" -ForegroundColor Yellow
}

# Clean previous builds if requested
if ($Clean) {
    Write-Host "Cleaning previous builds..." -ForegroundColor Yellow
    try {
        dotnet clean $ProjectFile -c $Configuration -v minimal
        Write-Host "Clean completed successfully" -ForegroundColor Green
    }
    catch {
        Write-Warning "Clean operation failed: $($_.Exception.Message)"
    }
}

# Restore NuGet packages
Write-Host "Restoring NuGet packages..." -ForegroundColor Yellow
try {
    dotnet restore $ProjectFile
    Write-Host "Package restore completed successfully" -ForegroundColor Green
}
catch {
    Write-Error "Package restore failed: $($_.Exception.Message)"
    exit 1
}

# Build parameters
$BuildParams = @(
    $ProjectFile
    "-c", $Configuration
    "-f", "net10.0-android"
    "-p:AndroidPackageFormat=apk"
    "-p:AndroidUseAapt2=true"
    "-p:AndroidCreatePackagePerAbi=false"
    "-p:EmbedAssembliesIntoApk=true"
    "-p:AndroidLinkMode=SdkOnly"
    "-p:AndroidEnableProfiledAot=true"
    "-p:RunAOTCompilation=true"
)

# Add keystore parameters if provided
if (-not [string]::IsNullOrEmpty($KeystorePath)) {
    if (Test-Path $KeystorePath) {
        Write-Host "Using keystore: $KeystorePath" -ForegroundColor Cyan
        $BuildParams += "-p:AndroidKeyStore=true"
        $BuildParams += "-p:AndroidSigningKeyStore=$KeystorePath"
        
        if (-not [string]::IsNullOrEmpty($KeyAlias)) {
            $BuildParams += "-p:AndroidSigningKeyAlias=$KeyAlias"
        }
        
        if (-not [string]::IsNullOrEmpty($KeystorePassword)) {
            $BuildParams += "-p:AndroidSigningStorePass=$KeystorePassword"
        }
        
        if (-not [string]::IsNullOrEmpty($KeyPassword)) {
            $BuildParams += "-p:AndroidSigningKeyPass=$KeyPassword"
        }
    }
    else {
        Write-Warning "Keystore file not found: $KeystorePath"
        Write-Host "Building unsigned APK..." -ForegroundColor Yellow
    }
}
else {
    Write-Host "No keystore specified - building unsigned APK" -ForegroundColor Yellow
}

# Add verbose output if requested
if ($Verbose) {
    $BuildParams += "-v", "detailed"
}

# Build the application
Write-Host "Building Android application..." -ForegroundColor Yellow
Write-Host "Command: dotnet publish $($BuildParams -join ' ')" -ForegroundColor Gray

try {
    $BuildStartTime = Get-Date
    dotnet publish @BuildParams
    $BuildEndTime = Get-Date
    $BuildDuration = $BuildEndTime - $BuildStartTime
    
    Write-Host "Build completed successfully in $($BuildDuration.TotalSeconds.ToString('F1')) seconds" -ForegroundColor Green
}
catch {
    Write-Error "Build failed: $($_.Exception.Message)"
    exit 1
}

# Find generated APK files
$BinPath = Join-Path $ProjectRoot "src\MauiFE\bin\$Configuration\net10.0-android"
$PublishPath = Join-Path $BinPath "publish"

Write-Host "Searching for generated APK files..." -ForegroundColor Yellow

$ApkFiles = @()
if (Test-Path $PublishPath) {
    $ApkFiles += Get-ChildItem -Path $PublishPath -Filter "*.apk" -Recurse
}
if (Test-Path $BinPath) {
    $ApkFiles += Get-ChildItem -Path $BinPath -Filter "*.apk" -Recurse
}

if ($ApkFiles.Count -eq 0) {
    Write-Warning "No APK files found in build output"
    Write-Host "Checked paths:" -ForegroundColor Gray
    Write-Host "  - $PublishPath" -ForegroundColor Gray
    Write-Host "  - $BinPath" -ForegroundColor Gray
}
else {
    Write-Host "Found $($ApkFiles.Count) APK file(s):" -ForegroundColor Green
    
    foreach ($apk in $ApkFiles) {
        $apkSize = [math]::Round($apk.Length / 1MB, 2)
        Write-Host "  - $($apk.Name) ($apkSize MB)" -ForegroundColor Cyan
        Write-Host "    Path: $($apk.FullName)" -ForegroundColor Gray
        
        # Copy APK to output directory
        $destinationPath = Join-Path $OutputPath $apk.Name
        Copy-Item -Path $apk.FullName -Destination $destinationPath -Force
        Write-Host "    Copied to: $destinationPath" -ForegroundColor Green
    }
}

# Generate build information file
$BuildInfo = @{
    BuildDate = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Configuration = $Configuration
    ProjectFile = $ProjectFile
    OutputPath = $OutputPath
    BuildDuration = $BuildDuration.TotalSeconds
    ApkFiles = $ApkFiles | ForEach-Object { 
        @{
            Name = $_.Name
            Size = $_.Length
            Path = $_.FullName
        }
    }
    Environment = @{
        DotNetVersion = (dotnet --version)
        OSVersion = [System.Environment]::OSVersion.ToString()
        MachineName = [System.Environment]::MachineName
    }
}

$BuildInfoPath = Join-Path $OutputPath "build-info.json"
$BuildInfo | ConvertTo-Json -Depth 3 | Out-File -FilePath $BuildInfoPath -Encoding UTF8
Write-Host "Build information saved to: $BuildInfoPath" -ForegroundColor Cyan

Write-Host "=== Build Process Completed ===" -ForegroundColor Green
Write-Host "Output directory: $OutputPath" -ForegroundColor Cyan

# Display next steps
Write-Host "`n=== Next Steps ===" -ForegroundColor Yellow
Write-Host "1. Test the APK on a device or emulator" -ForegroundColor White
Write-Host "2. For wireless deployment: Use 'adb install -r <apk-file>'" -ForegroundColor White
Write-Host "3. For OneDrive transfer: Upload APK to OneDrive and download on device" -ForegroundColor White
Write-Host "4. For store deployment: Upload to Google Play Console or Galaxy Store" -ForegroundColor White

if ($ApkFiles.Count -gt 0) {
    Write-Host "`nQuick deployment commands:" -ForegroundColor Yellow
    foreach ($apk in $ApkFiles) {
        $apkInOutput = Join-Path $OutputPath $apk.Name
        Write-Host "adb install -r `"$apkInOutput`"" -ForegroundColor Gray
    }
}
