# Wireless Android Deployment Script
# This script deploys the MAUI app to Samsung Galaxy S10+ via ADB over WiFi

param(
    [string]$DeviceIP = "",
    [int]$DevicePort = 5555,
    [string]$ApkPath = "",
    [switch]$BuildFirst = $false,
    [switch]$Verbose = $false,
    [switch]$ForceInstall = $true
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Get script directory and project root
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectRoot = Split-Path -Parent $ScriptDir

Write-Host "=== Wireless Android Deployment Script ===" -ForegroundColor Green

# Check if ADB is available
try {
    $adbVersion = adb version 2>&1 | Select-Object -First 1
    Write-Host "ADB found: $adbVersion" -ForegroundColor Green
}
catch {
    Write-Error "ADB not found. Please install Android SDK Platform Tools."
    Write-Host "Download from: https://developer.android.com/studio/releases/platform-tools" -ForegroundColor Yellow
    exit 1
}

# Build first if requested
if ($BuildFirst) {
    Write-Host "Building application first..." -ForegroundColor Yellow
    $buildScript = Join-Path $ScriptDir "build-android-release.ps1"
    
    if (Test-Path $buildScript) {
        & $buildScript -Verbose:$Verbose
        if ($LASTEXITCODE -ne 0) {
            Write-Error "Build failed"
            exit 1
        }
    }
    else {
        Write-Error "Build script not found: $buildScript"
        exit 1
    }
}

# Find APK file if not specified
if ([string]::IsNullOrEmpty($ApkPath)) {
    Write-Host "Searching for APK files..." -ForegroundColor Yellow
    
    $searchPaths = @(
        (Join-Path $ProjectRoot "artifacts\android"),
        (Join-Path $ProjectRoot "src\MauiFE\bin\Release\net10.0-android\publish"),
        (Join-Path $ProjectRoot "src\MauiFE\bin\Release\net10.0-android")
    )
    
    $apkFiles = @()
    foreach ($path in $searchPaths) {
        if (Test-Path $path) {
            $apkFiles += Get-ChildItem -Path $path -Filter "*.apk" -Recurse | Sort-Object LastWriteTime -Descending
        }
    }
    
    if ($apkFiles.Count -eq 0) {
        Write-Error "No APK files found. Please build the application first or specify -ApkPath"
        exit 1
    }
    
    $ApkPath = $apkFiles[0].FullName
    Write-Host "Using APK: $ApkPath" -ForegroundColor Cyan
}
else {
    if (-not (Test-Path $ApkPath)) {
        Write-Error "APK file not found: $ApkPath"
        exit 1
    }
}

# Get device IP if not specified
if ([string]::IsNullOrEmpty($DeviceIP)) {
    Write-Host "Device IP not specified. Please provide the IP address of your Samsung Galaxy S10+" -ForegroundColor Yellow
    Write-Host "You can find this in Settings > Developer options > Wireless debugging" -ForegroundColor Gray
    $DeviceIP = Read-Host "Enter device IP address"
    
    if ([string]::IsNullOrEmpty($DeviceIP)) {
        Write-Error "Device IP is required"
        exit 1
    }
}

$deviceAddress = "${DeviceIP}:${DevicePort}"
Write-Host "Target device: $deviceAddress" -ForegroundColor Cyan

# Kill any existing ADB server to ensure clean state
Write-Host "Restarting ADB server..." -ForegroundColor Yellow
try {
    adb kill-server | Out-Null
    Start-Sleep -Seconds 2
    adb start-server | Out-Null
    Write-Host "ADB server restarted" -ForegroundColor Green
}
catch {
    Write-Warning "Failed to restart ADB server: $($_.Exception.Message)"
}

# Connect to device
Write-Host "Connecting to device..." -ForegroundColor Yellow
try {
    $connectResult = adb connect $deviceAddress 2>&1
    Write-Host "Connection result: $connectResult" -ForegroundColor Gray
    
    # Wait a moment for connection to establish
    Start-Sleep -Seconds 3
}
catch {
    Write-Error "Failed to connect to device: $($_.Exception.Message)"
    exit 1
}

# Verify device connection
Write-Host "Verifying device connection..." -ForegroundColor Yellow
try {
    $devices = adb devices 2>&1
    Write-Host "Connected devices:" -ForegroundColor Gray
    Write-Host $devices -ForegroundColor Gray
    
    if ($devices -notmatch $DeviceIP) {
        Write-Error "Device not found in connected devices list"
        Write-Host "Troubleshooting steps:" -ForegroundColor Yellow
        Write-Host "1. Ensure wireless debugging is enabled on the device" -ForegroundColor White
        Write-Host "2. Check that both devices are on the same WiFi network" -ForegroundColor White
        Write-Host "3. Verify the IP address and port number" -ForegroundColor White
        Write-Host "4. Try disabling and re-enabling wireless debugging" -ForegroundColor White
        exit 1
    }
    
    Write-Host "Device connection verified" -ForegroundColor Green
}
catch {
    Write-Error "Failed to verify device connection: $($_.Exception.Message)"
    exit 1
}

# Get device information
Write-Host "Getting device information..." -ForegroundColor Yellow
try {
    $deviceModel = adb -s $deviceAddress shell getprop ro.product.model 2>&1
    $androidVersion = adb -s $deviceAddress shell getprop ro.build.version.release 2>&1
    $apiLevel = adb -s $deviceAddress shell getprop ro.build.version.sdk 2>&1
    
    Write-Host "Device Model: $deviceModel" -ForegroundColor Cyan
    Write-Host "Android Version: $androidVersion" -ForegroundColor Cyan
    Write-Host "API Level: $apiLevel" -ForegroundColor Cyan
}
catch {
    Write-Warning "Could not retrieve device information: $($_.Exception.Message)"
}

# Check if app is already installed
$packageName = "com.companyname.mauiapp10pushnotifications"
Write-Host "Checking if app is already installed..." -ForegroundColor Yellow

try {
    $installedPackages = adb -s $deviceAddress shell pm list packages $packageName 2>&1
    $isInstalled = $installedPackages -match $packageName
    
    if ($isInstalled) {
        Write-Host "App is already installed" -ForegroundColor Yellow
        if ($ForceInstall) {
            Write-Host "Force install enabled - will reinstall" -ForegroundColor Yellow
        }
        else {
            $reinstall = Read-Host "Do you want to reinstall? (y/N)"
            if ($reinstall -ne "y" -and $reinstall -ne "Y") {
                Write-Host "Installation cancelled" -ForegroundColor Yellow
                exit 0
            }
        }
    }
    else {
        Write-Host "App is not installed - proceeding with fresh installation" -ForegroundColor Green
    }
}
catch {
    Write-Warning "Could not check installed packages: $($_.Exception.Message)"
}

# Install APK
Write-Host "Installing APK..." -ForegroundColor Yellow
Write-Host "APK Path: $ApkPath" -ForegroundColor Gray

$installArgs = @("-s", $deviceAddress, "install")
if ($ForceInstall -or $isInstalled) {
    $installArgs += "-r"  # Replace existing application
}
$installArgs += $ApkPath

try {
    $installStartTime = Get-Date
    
    if ($Verbose) {
        Write-Host "Command: adb $($installArgs -join ' ')" -ForegroundColor Gray
    }
    
    $installResult = adb @installArgs 2>&1
    $installEndTime = Get-Date
    $installDuration = $installEndTime - $installStartTime
    
    Write-Host "Installation result: $installResult" -ForegroundColor Gray
    
    if ($installResult -match "Success") {
        Write-Host "Installation completed successfully in $($installDuration.TotalSeconds.ToString('F1')) seconds" -ForegroundColor Green
    }
    else {
        Write-Error "Installation failed: $installResult"
        
        # Provide troubleshooting suggestions
        Write-Host "`nTroubleshooting suggestions:" -ForegroundColor Yellow
        if ($installResult -match "INSTALL_FAILED_INSUFFICIENT_STORAGE") {
            Write-Host "- Free up storage space on the device" -ForegroundColor White
        }
        if ($installResult -match "INSTALL_FAILED_UPDATE_INCOMPATIBLE") {
            Write-Host "- Uninstall the existing app first" -ForegroundColor White
        }
        if ($installResult -match "INSTALL_PARSE_FAILED") {
            Write-Host "- Check if the APK is corrupted or incompatible" -ForegroundColor White
        }
        
        exit 1
    }
}
catch {
    Write-Error "Installation failed: $($_.Exception.Message)"
    exit 1
}

# Launch the application
Write-Host "Launching application..." -ForegroundColor Yellow
try {
    $launchResult = adb -s $deviceAddress shell am start -n "$packageName/crc6465553fbbbaa5a7a8.MainActivity" 2>&1
    Write-Host "Launch result: $launchResult" -ForegroundColor Gray
    
    if ($launchResult -match "Starting") {
        Write-Host "Application launched successfully" -ForegroundColor Green
    }
    else {
        Write-Warning "Application may not have launched properly: $launchResult"
    }
}
catch {
    Write-Warning "Failed to launch application: $($_.Exception.Message)"
    Write-Host "You can manually launch the app from the device" -ForegroundColor Yellow
}

# Display completion summary
Write-Host "`n=== Deployment Complete ===" -ForegroundColor Green
Write-Host "Device: $deviceAddress" -ForegroundColor Cyan
Write-Host "APK: $ApkPath" -ForegroundColor Cyan
Write-Host "Package: $packageName" -ForegroundColor Cyan

Write-Host "`n=== Next Steps ===" -ForegroundColor Yellow
Write-Host "1. Check the app on your Samsung Galaxy S10+" -ForegroundColor White
Write-Host "2. Grant notification permissions when prompted" -ForegroundColor White
Write-Host "3. Test push notification functionality" -ForegroundColor White
Write-Host "4. Monitor device logs if needed: adb -s $deviceAddress logcat" -ForegroundColor White

# Disconnect from device (optional)
$disconnect = Read-Host "`nDisconnect from device? (y/N)"
if ($disconnect -eq "y" -or $disconnect -eq "Y") {
    try {
        adb disconnect $deviceAddress | Out-Null
        Write-Host "Disconnected from device" -ForegroundColor Green
    }
    catch {
        Write-Warning "Failed to disconnect: $($_.Exception.Message)"
    }
}
