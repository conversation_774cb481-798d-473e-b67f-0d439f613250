# System Requirements and Prerequisites

This document outlines all prerequisites and system requirements for developing, building, and deploying the .NET MAUI Push Notification application to Samsung Galaxy S10+ devices.

## Table of Contents

1. [Development Machine Requirements](#development-machine-requirements)
2. [Target Device Requirements](#target-device-requirements)
3. [Network Requirements](#network-requirements)
4. [Cloud Services Requirements](#cloud-services-requirements)
5. [Installation and Setup Guide](#installation-and-setup-guide)
6. [Verification Steps](#verification-steps)

## Development Machine Requirements

### Operating System

**Supported Platforms:**

- Windows 10 version 1903 (build 18362) or higher
- Windows 11 (recommended)
- macOS 12.0 (Monterey) or higher
- Ubuntu 20.04 LTS or higher

**Recommended:**

- Windows 11 with latest updates
- 16GB RAM minimum, 32GB recommended
- SSD storage with at least 50GB free space

### .NET Development Environment

**Required Components:**

1. **.NET 10 SDK (Preview)**
   - Version: 10.0.0-preview.5 or later
   - Download: [.NET 10 Preview](https://dotnet.microsoft.com/download/dotnet/10.0)

2. **Visual Studio 2022**
   - Version: 17.12 or later
   - Edition: Community, Professional, or Enterprise
   - Required Workloads:
     - .NET Multi-platform App UI development
     - Mobile development with .NET

3. **Alternative IDEs (Optional):**
   - Visual Studio Code with C# extension
   - JetBrains Rider 2024.2 or later

### Android Development Tools

**Required Components:**

1. **Android SDK**
   - Minimum API Level: 24 (Android 7.0)
   - Target API Level: 34 (Android 14)
   - Build Tools: 34.0.0 or later

2. **Java Development Kit (JDK)**
   - Version: JDK 11 or JDK 17 (recommended)
   - Oracle JDK or OpenJDK

3. **Android SDK Platform Tools**
   - ADB (Android Debug Bridge)
   - Fastboot
   - Platform tools 34.0.0 or later

**Installation Paths:**

```text
Android SDK: %LOCALAPPDATA%\Android\Sdk
Platform Tools: %LOCALAPPDATA%\Android\Sdk\platform-tools
```

### Additional Development Tools

**Required:**

1. **Git**
   - Version: 2.30 or later
   - For version control and repository management

2. **PowerShell**
   - Version: 5.1 or PowerShell Core 7.0+
   - For build and deployment scripts

**Recommended:**

1. **Azure CLI**
   - Version: 2.50 or later
   - For Azure Notification Hubs management

2. **Windows Terminal**
   - Enhanced command-line experience

3. **Android Emulator**
   - For testing without physical device
   - Minimum: API 24, x86_64 architecture

## Target Device Requirements

### Samsung Galaxy S10+ Specifications

**Device Information:**

- Model: Samsung Galaxy S10+ (SM-G975F/DS)
- Release Year: 2019
- Chipset: Exynos 9820 or Snapdragon 855

**Operating System:**

- Android Version: 9.0 (minimum) to 12.0 (maximum supported)
- API Level: 28 (minimum) to 31 (maximum)
- One UI Version: 1.1 to 4.1

**Hardware Requirements:**

- RAM: 8GB or 12GB (sufficient for app)
- Storage: Minimum 2GB free space
- Network: WiFi 802.11 a/b/g/n/ac, Bluetooth 5.0

### Device Configuration

**Required Settings:**

1. **Developer Options:**

   ```text
   Settings → About phone → Build number (tap 7 times)
   ```

2. **USB Debugging (if using USB):**

   ```text
   Settings → Developer options → USB debugging
   ```

3. **Wireless Debugging (Android 11+):**

   ```text
   Settings → Developer options → Wireless debugging
   ```

4. **Install Unknown Apps:**

   ```text
   Settings → Biometrics and security → Install unknown apps
   ```

**Security Settings:**

- Samsung Knox: May need to be configured for enterprise deployments
- Biometric security: Compatible with app
- Secure folder: App can be installed in secure folder if needed

### Google Services

**Required:**

- Google Play Services: Latest version
- Google Play Store: Latest version
- Google Account: Signed in for Firebase Cloud Messaging

**Firebase Cloud Messaging:**

- FCM requires Google Play Services
- Device must be connected to internet
- Google Account recommended for optimal functionality

## Network Requirements

### Development Network

**WiFi Network:**

- Both development machine and Samsung Galaxy S10+ must be on same network
- Network should allow device-to-device communication
- Firewall should allow ADB connections (port 5555)

**Internet Connectivity:**

- Required for NuGet package downloads
- Azure Notification Hubs connectivity
- Firebase Cloud Messaging
- App store deployments

### Corporate Network Considerations

**Firewall Ports:**

- Port 5555: ADB over WiFi
- Port 443: HTTPS for Azure services
- Port 80/443: Firebase Cloud Messaging

**Proxy Settings:**

- Configure development tools for corporate proxy
- Ensure Azure and Firebase endpoints are accessible

## Cloud Services Requirements

### Azure Services

**Required:**

1. **Azure Subscription**
   - Active subscription with sufficient credits
   - Resource group creation permissions

2. **Azure Notification Hubs**
   - Standard tier recommended for production
   - Free tier available for development/testing

**Configuration:**

- Notification Hub namespace
- Notification Hub instance
- Connection string with appropriate permissions

### Firebase Services

**Required:**

1. **Firebase Project**

   - Google account required
   - Firebase Console access

2. **Firebase Cloud Messaging**
   - Enabled for the project
   - Server key for Azure Notification Hubs integration

**Configuration Files:**

- `google-services.json` for Android
- Proper package name configuration

### OneDrive (for APK Transfer)

**Requirements:**

- Microsoft account
- OneDrive app installed on Samsung Galaxy S10+
- Sufficient storage space (minimum 100MB)

## Installation and Setup Guide

### Step 1: Install Development Environment

1. **Install .NET 10 SDK:**

   ```powershell
   # Download and install from Microsoft
   # Verify installation
   dotnet --version
   ```

2. **Install Visual Studio 2022:**
   - Download from [Visual Studio](https://visualstudio.microsoft.com/)
   - Select MAUI workload during installation

3. **Configure Android SDK:**

   ```powershell
   # Set environment variables
   $env:ANDROID_HOME = "$env:LOCALAPPDATA\Android\Sdk"
   $env:PATH += ";$env:ANDROID_HOME\platform-tools"
   ```

### Step 2: Clone and Setup Project

1. **Clone Repository:**

   ```powershell
   git clone https://github.com/your-repo/push-notification.git
   cd push-notification
   ```

2. **Restore Packages:**

   ```powershell
   dotnet restore src/MauiFE/MauiFrontEnd.csproj
   ```

3. **Build Project:**

   ```powershell
   dotnet build src/MauiFE/MauiFrontEnd.csproj -c Debug
   ```

### Step 3: Configure Cloud Services

1. **Setup Azure Notification Hubs:**
   - Follow [Azure Notification Hubs Setup Guide](AZURE_NOTIFICATION_HUBS_SETUP.md)
   - Deploy infrastructure using provided Bicep templates

2. **Configure Firebase:**
   - Create Firebase project
   - Download `google-services.json`
   - Place in `src/MauiFE/Platforms/Android/`

### Step 4: Prepare Samsung Galaxy S10+

1. **Enable Developer Options:**

   ```text
   Settings → About phone → Build number (tap 7 times)
   ```

2. **Configure Debugging:**

   ```text
   Settings → Developer options → USB debugging (enable)
   Settings → Developer options → Wireless debugging (enable)
   ```

3. **Install Required Apps:**
   - OneDrive (for APK transfer method)
   - Update Google Play Services

## Verification Steps

### Development Environment Verification

```powershell
# Run verification script
function Test-DevelopmentEnvironment {
    Write-Host "=== Development Environment Check ===" -ForegroundColor Green
    
    # Check .NET SDK
    try {
        $dotnetVersion = dotnet --version
        Write-Host "✓ .NET SDK: $dotnetVersion" -ForegroundColor Green
    } catch {
        Write-Host "✗ .NET SDK not found" -ForegroundColor Red
    }
    
    # Check Android SDK
    try {
        $adbVersion = adb version 2>&1 | Select-Object -First 1
        Write-Host "✓ Android SDK: $adbVersion" -ForegroundColor Green
    } catch {
        Write-Host "✗ Android SDK not found" -ForegroundColor Red
    }
    
    # Check Java
    try {
        $javaVersion = java -version 2>&1 | Select-Object -First 1
        Write-Host "✓ Java: $javaVersion" -ForegroundColor Green
    } catch {
        Write-Host "✗ Java not found" -ForegroundColor Red
    }
    
    # Check project build
    try {
        $buildResult = dotnet build src/MauiFE/MauiFrontEnd.csproj -c Debug --verbosity quiet
        Write-Host "✓ Project builds successfully" -ForegroundColor Green
    } catch {
        Write-Host "✗ Project build failed" -ForegroundColor Red
    }
}

Test-DevelopmentEnvironment
```

### Device Connectivity Verification

```powershell
# Check device connection
function Test-DeviceConnection {
    param([string]$DeviceIP)
    
    Write-Host "=== Device Connection Check ===" -ForegroundColor Green
    
    # Test ADB connection
    try {
        if ($DeviceIP) {
            adb connect "${DeviceIP}:5555"
        }
        
        $devices = adb devices
        Write-Host "Connected devices:" -ForegroundColor Yellow
        Write-Host $devices -ForegroundColor Gray
        
        if ($devices -match "device$") {
            Write-Host "✓ Device connected successfully" -ForegroundColor Green
        } else {
            Write-Host "✗ No devices connected" -ForegroundColor Red
        }
    } catch {
        Write-Host "✗ ADB connection failed" -ForegroundColor Red
    }
}

# Usage: Test-DeviceConnection -DeviceIP "*************"
```

### Cloud Services Verification

```powershell
# Test Azure Notification Hub
az notification-hub test-send `
    --resource-group "your-resource-group" `
    --namespace-name "your-namespace" `
    --notification-hub-name "your-hub" `
    --notification-format "fcm" `
    --message "Test notification"
```

## Troubleshooting Common Setup Issues

### .NET SDK Issues

- Ensure .NET 10 SDK is installed, not just runtime
- Check PATH environment variable includes .NET tools
- Restart Visual Studio after SDK installation

### Android SDK Issues

- Verify ANDROID_HOME environment variable
- Check Android SDK Manager for missing components
- Update Android SDK Platform Tools

### Device Connection Issues

- Ensure both devices on same WiFi network
- Check Windows Firewall settings
- Try USB connection first, then wireless

### Build Issues

- Clean and restore NuGet packages
- Check project file for correct target frameworks
- Verify all required workloads are installed

For additional troubleshooting, refer to the [Troubleshooting Guide](TROUBLESHOOTING_GUIDE.md).
