# .NET MAUI Push Notifications Application

A comprehensive .NET MAUI application demonstrating push notifications using Azure Notification Hubs and Firebase Cloud Messaging, specifically optimized for Samsung Galaxy S10+ deployment.

## Overview

This application showcases a production-ready implementation of push notifications in .NET MAUI, featuring:

- Azure Notification Hubs integration for cross-platform push notifications
- Firebase Cloud Messaging (FCM) for Android devices
- Clean architecture with MVVM pattern
- Comprehensive error handling and logging
- Multiple deployment strategies for Samsung Galaxy S10+

## Quick Start

### Prerequisites

- .NET 10 SDK (Preview)
- Visual Studio 2022 (17.12+) with MAUI workload
- Android SDK (API Level 24+)
- Azure subscription
- Firebase project

### Build and Deploy

1. **Clone the repository:**
   ```powershell
   git clone https://github.com/your-repo/push-notification.git
   cd push-notification
   ```

2. **Build release APK:**
   ```powershell
   .\scripts\build-android-release.ps1
   ```

3. **Deploy to Samsung Galaxy S10+:**
   ```powershell
   # Wireless deployment
   .\scripts\deploy-wireless.ps1 -DeviceIP "*************"
   
   # Or use OneDrive transfer method (see deployment guide)
   ```

## Documentation

### Deployment Guides

- **[Android Deployment Guide](docs/ANDROID_DEPLOYMENT_GUIDE.md)** - Comprehensive deployment methods for Samsung Galaxy S10+
- **[System Requirements](docs/SYSTEM_REQUIREMENTS.md)** - Prerequisites and setup requirements
- **[Troubleshooting Guide](docs/TROUBLESHOOTING_GUIDE.md)** - Common issues and solutions

### Configuration Guides

- **[Azure Notification Hubs Setup](docs/AZURE_NOTIFICATION_HUBS_SETUP.md)** - Cloud services configuration
- **[Infrastructure Deployment](infra/README.md)** - Azure infrastructure as code

### Development Guides

- **[Compatibility Frame Implementation](docs/COMPATIBILITY_FRAME_IMPLEMENTATION.md)** - Custom control implementation

## Deployment Methods

### Method 1: Wireless ADB Deployment (Recommended)

Perfect for development and testing without USB cables:

```powershell
# Enable wireless debugging on Samsung Galaxy S10+
# Settings → Developer options → Wireless debugging

# Deploy using provided script
.\scripts\deploy-wireless.ps1 -DeviceIP "your-device-ip"
```

### Method 2: APK Sideloading via OneDrive

Ideal when USB debugging is disabled by company policy:

1. Build release APK using build script
2. Upload APK to OneDrive
3. Download and install on Samsung Galaxy S10+
4. Configure push notification permissions

### Method 3: App Store Deployment

For production distribution:

- **Google Play Store:** Standard Android app distribution
- **Samsung Galaxy Store:** Samsung-specific app store

## Project Structure

```
├── src/
│   └── MauiFE/                    # Main MAUI application
│       ├── Pages/                 # UI pages and views
│       ├── PageModels/            # MVVM view models
│       ├── Services/              # Business logic services
│       ├── Models/                # Data models
│       ├── Platforms/             # Platform-specific code
│       │   └── Android/           # Android-specific implementations
│       └── Resources/             # App resources and assets
├── docs/                          # Documentation
├── scripts/                       # Build and deployment scripts
├── infra/                         # Infrastructure as Code
└── tests/                         # Unit and integration tests
```

## Key Features

### Push Notification Services

- **Azure Notification Hubs Integration:** Centralized push notification management
- **Firebase Cloud Messaging:** Android-specific push notifications
- **Device Registration:** Automatic device token management
- **Error Handling:** Comprehensive error logging and recovery

### Samsung Galaxy S10+ Optimizations

- **Battery Optimization Handling:** Prevents app from being killed
- **Samsung Knox Compatibility:** Works with enterprise security
- **One UI Integration:** Follows Samsung design guidelines
- **Performance Optimization:** AOT compilation and linking optimizations

### Development Tools

- **Build Scripts:** Automated APK generation with signing
- **Deployment Scripts:** Wireless and USB deployment automation
- **Keystore Management:** Secure APK signing configuration
- **Diagnostic Tools:** Comprehensive troubleshooting utilities

## Configuration

### Azure Notification Hubs

Update `MauiProgram.cs` with your Azure configuration:

```csharp
builder.Services.Configure<NotificationHubOptions>(options =>
{
    options.Name = "your-notification-hub-name";
    options.ConnectionString = "your-connection-string";
});
```

### Firebase Configuration

1. Place `google-services.json` in `src/MauiFE/Platforms/Android/`
2. Ensure package name matches: `com.companyname.mauiapp10pushnotifications`

### Build Configuration

For signed releases, configure keystore in project file:

```xml
<PropertyGroup Condition="'$(Configuration)' == 'Release'">
    <AndroidKeyStore>true</AndroidKeyStore>
    <AndroidSigningKeyStore>path-to-keystore.jks</AndroidSigningKeyStore>
    <AndroidSigningKeyAlias>your-alias</AndroidSigningKeyAlias>
    <!-- Additional signing properties -->
</PropertyGroup>
```

## Scripts and Automation

### Build Scripts

- **`build-android-release.ps1`** - Builds signed release APK
- **`generate-keystore.ps1`** - Creates Android signing keystore
- **`deploy-wireless.ps1`** - Deploys via ADB over WiFi

### Usage Examples

```powershell
# Generate signing keystore
.\scripts\generate-keystore.ps1 -KeystoreName "my-app-key.jks"

# Build with custom keystore
.\scripts\build-android-release.ps1 -KeystorePath "certificates\my-app-key.jks"

# Deploy with build
.\scripts\deploy-wireless.ps1 -DeviceIP "*************" -BuildFirst
```

## Testing

### Unit Tests

```powershell
dotnet test tests/
```

### Device Testing

1. Install app on Samsung Galaxy S10+
2. Grant notification permissions
3. Test push notification registration
4. Send test notifications from Azure portal

### Integration Testing

- Test Azure Notification Hubs connectivity
- Verify Firebase Cloud Messaging integration
- Validate cross-platform notification delivery

## Troubleshooting

### Common Issues

1. **Installation Blocked:** Enable "Install unknown apps" in device settings
2. **Notifications Not Working:** Check notification permissions and Google Play Services
3. **Build Failures:** Verify .NET 10 SDK and Android SDK installation
4. **Connection Issues:** Ensure devices on same WiFi network

### Diagnostic Commands

```powershell
# Check connected devices
adb devices

# View app logs
adb logcat -s "MauiApp10PushNotifications"

# Test notification hub
az notification-hub test-send --resource-group "rg" --namespace-name "ns" --notification-hub-name "hub"
```

For detailed troubleshooting, see [Troubleshooting Guide](docs/TROUBLESHOOTING_GUIDE.md).

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:

1. Check the [Troubleshooting Guide](docs/TROUBLESHOOTING_GUIDE.md)
2. Review [System Requirements](docs/SYSTEM_REQUIREMENTS.md)
3. Consult [Azure Notification Hubs documentation](https://docs.microsoft.com/azure/notification-hubs/)
4. Open an issue in the repository

## Changelog

See [AUDITTRAIL.md](AUDITTRAIL.md) for detailed change history and development progress.
