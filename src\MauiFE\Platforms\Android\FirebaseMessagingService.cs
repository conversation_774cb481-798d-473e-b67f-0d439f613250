using Android.App;
using Android.Content;
using AndroidX.Core.App;
using Firebase.Messaging;
using MauiApp10PushNotifications.Models;
using MauiApp10PushNotifications.Services;

namespace MauiApp10PushNotifications.Platforms.Android;

/// <summary>
/// Firebase Cloud Messaging service for handling push notifications on Android
/// </summary>
[Service(Exported = true)]
[IntentFilter(new[] { "com.google.firebase.MESSAGING_EVENT" })]
public class FirebaseMessagingService : Firebase.Messaging.FirebaseMessagingService
{
    private const string ChannelId = "push_notifications";
    private const string ChannelName = "Push Notifications";
    private const string ChannelDescription = "Notifications from the app";

    private IDeviceInstallationService? deviceInstallationService;
    private INotificationRegistrationService? registrationService;
    private INotificationActionService? actionService;
    private IPushNotificationService? pushNotificationService;

    /// <summary>
    /// Called when a new FCM token is generated
    /// </summary>
    public override void OnNewToken(string token)
    {
        try
        {
            System.Diagnostics.Debug.WriteLine($"FCM Token: {token}");

            // Update the token in the device installation service
            if (DeviceInstallationService != null)
            {
                DeviceInstallationService.Token = token;

                // Refresh registration with the new token
                Task.Run(async () =>
                {
                    try
                    {
                        if (RegistrationService != null)
                        {
                            await RegistrationService.RefreshRegistrationAsync();
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Error refreshing registration: {ex.Message}");
                    }
                });
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error in OnNewToken: {ex.Message}");
        }
    }

    /// <summary>
    /// Called when a message is received
    /// </summary>
    public override void OnMessageReceived(RemoteMessage message)
    {
        try
        {
            System.Diagnostics.Debug.WriteLine($"FCM Message received: {message.MessageId}");

            var notification = CreatePushNotification(message);

            // Handle the notification based on app state
            if (IsAppInForeground())
            {
                // App is in foreground - notify the service
                PushNotificationService?.HandleReceivedNotification(notification);
            }
            else
            {
                // App is in background - show system notification
                ShowNotification(notification);
            }

            // Handle any action data
            if (message.Data.TryGetValue("action", out var action) && !string.IsNullOrEmpty(action))
            {
                ActionService?.TriggerAction(action);
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error in OnMessageReceived: {ex.Message}");
        }
    }

    private PushNotification CreatePushNotification(RemoteMessage message)
    {
        var notification = new PushNotification
        {
            ReceivedAt = DateTime.Now
        };

        // Extract notification data
        var remoteNotification = message.GetNotification();
        if (remoteNotification != null)
        {
            notification.Title = remoteNotification.Title ?? string.Empty;
            notification.Body = remoteNotification.Body ?? string.Empty;
            notification.IsSilent = false;
        }
        else
        {
            // Silent notification - data only
            notification.IsSilent = true;
            if (message.Data.TryGetValue("title", out var title))
                notification.Title = title;
            if (message.Data.TryGetValue("body", out var body))
                notification.Body = body;
        }

        // Extract action
        if (message.Data.TryGetValue("action", out var action))
        {
            notification.Action = action;
        }

        // Copy all data
        foreach (var kvp in message.Data)
        {
            notification.Data[kvp.Key] = kvp.Value;
        }

        return notification;
    }

    private void ShowNotification(PushNotification notification)
    {
        try
        {
            var context = ApplicationContext;
            if (context == null) return;

            var notificationManager = NotificationManagerCompat.From(context);

            // Create notification channel (required for Android 8.0+)
            CreateNotificationChannel(context);

            // Create intent for when notification is tapped
            var intent = new Intent(context, typeof(MainActivity));
            intent.AddFlags(ActivityFlags.ClearTop);
            intent.PutExtra("action", notification.Action);

            var flags = PendingIntentFlags.OneShot;
            if (global::Android.OS.Build.VERSION.SdkInt >= global::Android.OS.BuildVersionCodes.M)
            {
                flags |= PendingIntentFlags.Immutable;
            }

            var pendingIntent = PendingIntent.GetActivity(
                context,
                0,
                intent,
                flags);

            // Build the notification
            var notificationBuilder = new NotificationCompat.Builder(context, ChannelId)
                .SetSmallIcon(Resource.Drawable.notification_icon_background) // You'll need to add this icon
                .SetContentTitle(notification.Title)
                .SetContentText(notification.Body)
                .SetAutoCancel(true)
                .SetContentIntent(pendingIntent)
                .SetPriority(NotificationCompat.PriorityDefault);

            // Show the notification
            notificationManager.Notify(GetNotificationId(), notificationBuilder.Build());
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error showing notification: {ex.Message}");
        }
    }

    private void CreateNotificationChannel(Context context)
    {
        if (global::Android.OS.Build.VERSION.SdkInt >= global::Android.OS.BuildVersionCodes.O)
        {
            var notificationManager = context.GetSystemService(Context.NotificationService) as NotificationManager;

            if (notificationManager?.GetNotificationChannel(ChannelId) == null)
            {
                var channel = new NotificationChannel(ChannelId, ChannelName, NotificationImportance.Default);

                // Set description only if supported (API 26+)
                if (global::Android.OS.Build.VERSION.SdkInt >= global::Android.OS.BuildVersionCodes.O)
                {
                    channel.Description = ChannelDescription;
                }

                notificationManager?.CreateNotificationChannel(channel);
            }
        }
    }

    private bool IsAppInForeground()
    {
        try
        {
            var activityManager = GetSystemService(Context.ActivityService) as ActivityManager;
            var runningAppProcesses = activityManager?.RunningAppProcesses;

            if (runningAppProcesses != null)
            {
                foreach (var processInfo in runningAppProcesses)
                {
                    if (processInfo.ProcessName == PackageName &&
                        processInfo.Importance == global::Android.App.Importance.Foreground)
                    {
                        return true;
                    }
                }
            }

            return false;
        }
        catch
        {
            return false;
        }
    }

    private int GetNotificationId()
    {
        return (int)DateTimeOffset.UtcNow.ToUnixTimeSeconds();
    }

    // Lazy-loaded service properties
    private IDeviceInstallationService? DeviceInstallationService =>
        deviceInstallationService ??= IPlatformApplication.Current?.Services?.GetService<IDeviceInstallationService>();

    private INotificationRegistrationService? RegistrationService =>
        registrationService ??= IPlatformApplication.Current?.Services?.GetService<INotificationRegistrationService>();

    private INotificationActionService? ActionService =>
        actionService ??= IPlatformApplication.Current?.Services?.GetService<INotificationActionService>();

    private IPushNotificationService? PushNotificationService =>
        pushNotificationService ??= IPlatformApplication.Current?.Services?.GetService<IPushNotificationService>();
}
