# Troubleshooting Guide - Samsung Galaxy S10+ Deployment

This guide addresses common issues when deploying the .NET MAUI Push Notification application to Samsung Galaxy S10+ devices.

## Table of Contents

1. [Installation Issues](#installation-issues)
2. [Samsung-Specific Restrictions](#samsung-specific-restrictions)
3. [Push Notification Problems](#push-notification-problems)
4. [Network and Connectivity Issues](#network-and-connectivity-issues)
5. [Build and Development Issues](#build-and-development-issues)
6. [Performance and Runtime Issues](#performance-and-runtime-issues)
7. [Diagnostic Tools and Commands](#diagnostic-tools-and-commands)

## Installation Issues

### APK Installation Blocked

**Problem:** "Installation blocked" or "App not installed" error

**Solutions:**

1. **Enable Unknown Sources:**

   ```plaintext
   Settings → Biometrics and security → Install unknown apps
   Select the source app (OneDrive, Files, etc.) → Allow from this source
   ```

2. **Disable Samsung Knox Security:**

   ```
   Settings → Biometrics and security → Other security settings
   Disable "Scan device for security threats"
   ```

3. **Check Storage Space:**
   - Ensure at least 500MB free space
   - Clear cache if needed: Settings → Device care → Storage

4. **Verify APK Integrity:**

   ```powershell
   # Check APK file size and integrity
   Get-FileHash -Path "path\to\your.apk" -Algorithm SHA256
   ```

### Installation Fails with "INSTALL_FAILED_UPDATE_INCOMPATIBLE"

**Problem:** Cannot update existing app installation

**Solutions:**

1. **Uninstall Previous Version:**

   ```
   Settings → Apps → MauiApp10PushNotifications → Uninstall
   ```

2. **Clear App Data:**

   ```
   Settings → Apps → MauiApp10PushNotifications → Storage → Clear Data
   ```

3. **Use ADB to Force Reinstall:**

   ```powershell
   adb install -r "path\to\your.apk"
   ```

### "Parse Error" During Installation

**Problem:** APK file cannot be parsed

**Solutions:**

1. **Verify Target Android Version:**
   - Samsung Galaxy S10+ supports Android 9.0+ (API 28+)
   - Check project targets: `net10.0-android` with `SupportedOSPlatformVersion`

2. **Rebuild APK:**

   ```powershell
   dotnet clean src/MauiFE/MauiFrontEnd.csproj
   dotnet publish src/MauiFE/MauiFrontEnd.csproj -c Release -f net10.0-android
   ```

3. **Check APK Signing:**
   - Ensure APK is properly signed
   - Use debug keystore for testing

## Samsung-Specific Restrictions

### Samsung Knox Security

**Problem:** Knox security blocking app installation or functionality

**Solutions:**

1. **Disable Knox Security Temporarily:**

   ```
   Settings → Biometrics and security → Other security settings
   Turn off "Scan device for security threats"
   ```

2. **Add App to Knox Whitelist:**
   - Contact Samsung Knox administrator if in enterprise environment
   - Request app whitelisting for corporate devices

### Samsung Battery Optimization

**Problem:** App killed by aggressive battery optimization

**Solutions:**

1. **Disable Battery Optimization:**

   ```
   Settings → Device care → Battery → More battery settings
   → Optimize battery usage → All apps
   Find "MauiApp10PushNotifications" → Don't optimize
   ```

2. **Add to Never Sleeping Apps:**

   ```
   Settings → Device care → Battery → More battery settings
   → Never sleeping apps → Add "MauiApp10PushNotifications"
   ```

### Samsung One UI Restrictions

**Problem:** One UI blocking notifications or background activity

**Solutions:**

1. **Configure App Permissions:**

   ```
   Settings → Apps → MauiApp10PushNotifications → Permissions
   Enable all required permissions
   ```

2. **Allow Background Activity:**

   ```
   Settings → Apps → MauiApp10PushNotifications
   → Battery → Allow background activity
   ```

## Push Notification Problems

### Notifications Not Received

**Problem:** Push notifications not appearing on device

**Diagnostic Steps:**

1. **Check Notification Permissions:**

   ```
   Settings → Apps → MauiApp10PushNotifications → Permissions
   Ensure "Notifications" is enabled
   ```

2. **Verify Firebase Token:**
   - Open app and check Push Notifications page
   - Ensure device token is displayed
   - Test registration status

3. **Check Azure Notification Hub:**

   ```powershell
   # Test notification hub connectivity
   az notification-hub test-send --resource-group "your-rg" --namespace-name "your-namespace" --notification-hub-name "your-hub"
   ```

**Solutions:**

1. **Reset Notification Settings:**

   ```
   Settings → Apps → MauiApp10PushNotifications → Notifications
   Turn off all notifications, restart app, turn back on
   ```

2. **Clear Google Play Services Cache:**

   ```
   Settings → Apps → Google Play services → Storage → Clear Cache
   ```

3. **Re-register Device:**
   - Open app → Push Notifications page
   - Tap "Unregister Device" then "Register Device"

### Firebase Connection Issues

**Problem:** Firebase Cloud Messaging not working

**Solutions:**

1. **Verify google-services.json:**
   - Ensure file is in `Platforms/Android/` folder
   - Check package name matches: `com.companyname.mauiapp10pushnotifications`

2. **Update Google Play Services:**

   ```
   Google Play Store → Search "Google Play services" → Update
   ```

3. **Check Firebase Project Configuration:**
   - Verify SHA-1 fingerprint in Firebase Console
   - Ensure FCM is enabled for the project

## Network and Connectivity Issues

### Wireless ADB Connection Fails

**Problem:** Cannot connect to device via ADB over WiFi

**Solutions:**

1. **Check Network Configuration:**

   ```powershell
   # Verify both devices on same network
   ipconfig
   # On device: Settings → About phone → Status → IP address
   ```

2. **Restart ADB Server:**

   ```powershell
   adb kill-server
   adb start-server
   adb connect [device-ip]:5555
   ```

3. **Check Firewall Settings:**
   - Disable Windows Firewall temporarily
   - Add ADB to firewall exceptions

4. **Alternative Connection Methods:**

   ```powershell
   # Try different port
   adb connect [device-ip]:5037
   
   # Use TCP/IP over USB first
   adb tcpip 5555
   adb connect [device-ip]:5555
   ```

### OneDrive Download Issues

**Problem:** Cannot download APK from OneDrive

**Solutions:**

1. **Check Internet Connection:**
   - Verify WiFi/mobile data connectivity
   - Test with other downloads

2. **Clear OneDrive Cache:**

   ```
   Settings → Apps → OneDrive → Storage → Clear Cache
   ```

3. **Use Alternative Transfer Methods:**
   - Email APK to yourself
   - Use Google Drive or Dropbox
   - Transfer via USB cable if available

## Build and Development Issues

### Build Failures

**Problem:** Cannot build release APK

**Solutions:**

1. **Clean and Restore:**

   ```powershell
   dotnet clean
   dotnet restore
   dotnet build -c Release
   ```

2. **Check .NET 10 SDK:**

   ```powershell
   dotnet --version
   # Should show 10.0.x
   ```

3. **Update NuGet Packages:**

   ```powershell
   dotnet list package --outdated
   dotnet add package [PackageName] --version [LatestVersion]
   ```

### Signing Errors

**Problem:** APK signing failures

**Solutions:**

1. **Generate New Keystore:**

   ```powershell
   .\scripts\generate-keystore.ps1
   ```

2. **Verify Keystore Configuration:**
   - Check paths in project file
   - Verify passwords are correct

3. **Use Debug Keystore for Testing:**

   ```xml
   <AndroidKeyStore>false</AndroidKeyStore>
   ```

## Performance and Runtime Issues

### App Crashes on Launch

**Problem:** Application crashes immediately after launch

**Solutions:**

1. **Check Device Logs:**

   ```powershell
   adb logcat -s "MauiApp10PushNotifications"
   ```

2. **Verify Permissions:**
   - Ensure all required permissions are granted
   - Check AndroidManifest.xml

3. **Test on Emulator:**
   - Create Android emulator with similar specs
   - Compare behavior between device and emulator

### Slow Performance

**Problem:** App runs slowly on Samsung Galaxy S10+

**Solutions:**

1. **Enable AOT Compilation:**

   ```xml
   <RunAOTCompilation>true</RunAOTCompilation>
   <AndroidEnableProfiledAot>true</AndroidEnableProfiledAot>
   ```

2. **Optimize Linking:**

   ```xml
   <AndroidLinkMode>SdkOnly</AndroidLinkMode>
   ```

3. **Reduce App Size:**

   ```xml
   <AndroidCreatePackagePerAbi>true</AndroidCreatePackagePerAbi>
   ```

## Diagnostic Tools and Commands

### ADB Commands

```powershell
# List connected devices
adb devices

# Install APK with verbose output
adb install -r -d "path\to\app.apk"

# View device logs
adb logcat

# Filter logs for your app
adb logcat -s "MauiApp10PushNotifications"

# Get device information
adb shell getprop ro.product.model
adb shell getprop ro.build.version.release

# Check installed packages
adb shell pm list packages | findstr "mauiapp10pushnotifications"

# Clear app data
adb shell pm clear com.companyname.mauiapp10pushnotifications

# Force stop app
adb shell am force-stop com.companyname.mauiapp10pushnotifications
```

### PowerShell Diagnostic Script

```powershell
# Quick diagnostic script
function Test-AndroidDeployment {
    Write-Host "=== Android Deployment Diagnostics ===" -ForegroundColor Green
    
    # Check ADB
    try {
        $adbVersion = adb version 2>&1 | Select-Object -First 1
        Write-Host "✓ ADB: $adbVersion" -ForegroundColor Green
    } catch {
        Write-Host "✗ ADB not found" -ForegroundColor Red
    }
    
    # Check .NET SDK
    try {
        $dotnetVersion = dotnet --version
        Write-Host "✓ .NET SDK: $dotnetVersion" -ForegroundColor Green
    } catch {
        Write-Host "✗ .NET SDK not found" -ForegroundColor Red
    }
    
    # Check connected devices
    try {
        $devices = adb devices 2>&1
        Write-Host "Connected devices:" -ForegroundColor Yellow
        Write-Host $devices -ForegroundColor Gray
    } catch {
        Write-Host "✗ Cannot list devices" -ForegroundColor Red
    }
}

# Run diagnostics
Test-AndroidDeployment
```

### Log Analysis

When analyzing logs, look for these common patterns:

- **Permission Denied:** Check app permissions
- **ClassNotFoundException:** Missing dependencies
- **OutOfMemoryError:** Increase heap size or optimize app
- **NetworkSecurityException:** Check network security config
- **Firebase errors:** Verify google-services.json configuration

## Getting Additional Help

If issues persist:

1. **Check Device Compatibility:**
   - Verify Samsung Galaxy S10+ Android version
   - Check minimum API level requirements

2. **Test on Different Device:**
   - Try installation on another Android device
   - Compare results to isolate device-specific issues

3. **Review Application Logs:**
   - Enable verbose logging in the app
   - Analyze crash reports and error messages

4. **Contact Support:**
   - Provide device model, Android version, and error messages
   - Include relevant log files and screenshots
