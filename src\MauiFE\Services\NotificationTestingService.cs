using MauiApp10PushNotifications.Models;
using System.Text.Json;

namespace MauiApp10PushNotifications.Services;

/// <summary>
/// Service for testing and validating push notification functionality
/// </summary>
public class NotificationTestingService
{
    private readonly IPushNotificationService pushNotificationService;
    private readonly IDeviceInstallationService deviceInstallationService;
    private readonly ILogger<NotificationTestingService> logger;
    private readonly List<NotificationTestResult> testResults = new();

    public NotificationTestingService(
        IPushNotificationService pushNotificationService,
        IDeviceInstallationService deviceInstallationService,
        ILogger<NotificationTestingService> logger)
    {
        this.pushNotificationService = pushNotificationService;
        this.deviceInstallationService = deviceInstallationService;
        this.logger = logger;
    }

    /// <summary>
    /// Event fired when a test is completed
    /// </summary>
    public event EventHandler<NotificationTestResult>? TestCompleted;

    /// <summary>
    /// Gets the history of test results
    /// </summary>
    public IReadOnlyList<NotificationTestResult> TestResults => this.testResults.AsReadOnly();

    /// <summary>
    /// Runs a comprehensive test suite for push notifications
    /// </summary>
    public async Task<NotificationTestSuite> RunTestSuiteAsync()
    {
        var suite = new NotificationTestSuite
        {
            StartTime = DateTime.Now,
            TestName = "Push Notification Comprehensive Test"
        };

        this.logger.LogInformation("Starting push notification test suite");

        // Test 1: Device Support Check
        var supportTest = await TestDeviceSupportAsync();
        suite.Results.Add(supportTest);

        // Test 2: Service Initialization
        var initTest = await TestServiceInitializationAsync();
        suite.Results.Add(initTest);

        // Test 3: Device Registration
        var registrationTest = await TestDeviceRegistrationAsync();
        suite.Results.Add(registrationTest);

        // Test 4: Token Validation
        var tokenTest = await TestTokenValidationAsync();
        suite.Results.Add(tokenTest);

        // Test 5: Test Notification Send
        var sendTest = await TestNotificationSendAsync();
        suite.Results.Add(sendTest);

        // Test 6: Silent Notification
        var silentTest = await TestSilentNotificationAsync();
        suite.Results.Add(silentTest);

        suite.EndTime = DateTime.Now;
        suite.Duration = suite.EndTime - suite.StartTime;
        suite.PassedTests = suite.Results.Count(r => r.Passed);
        suite.FailedTests = suite.Results.Count(r => !r.Passed);
        suite.TotalTests = suite.Results.Count;

        this.logger.LogInformation("Test suite completed: {PassedTests}/{TotalTests} tests passed",
            suite.PassedTests, suite.TotalTests);

        return suite;
    }

    /// <summary>
    /// Tests if the device supports push notifications
    /// </summary>
    public Task<NotificationTestResult> TestDeviceSupportAsync()
    {
        var test = new NotificationTestResult
        {
            TestName = "Device Support Check",
            StartTime = DateTime.Now
        };

        try
        {
            var isSupported = this.pushNotificationService.IsSupported;
            var notificationsSupported = this.deviceInstallationService.NotificationsSupported;

            test.Passed = isSupported && notificationsSupported;
            test.Message = test.Passed
                ? "Device supports push notifications"
                : "Device does not support push notifications";

            test.Details = $"Service supported: {isSupported}, Device supported: {notificationsSupported}";
        }
        catch (Exception ex)
        {
            test.Passed = false;
            test.Message = $"Error checking device support: {ex.Message}";
            test.Exception = ex;
        }
        finally
        {
            test.EndTime = DateTime.Now;
            test.Duration = test.EndTime - test.StartTime;
            AddTestResult(test);
        }

        return Task.FromResult(test);
    }

    /// <summary>
    /// Tests service initialization
    /// </summary>
    public async Task<NotificationTestResult> TestServiceInitializationAsync()
    {
        var test = new NotificationTestResult
        {
            TestName = "Service Initialization",
            StartTime = DateTime.Now
        };

        try
        {
            await this.pushNotificationService.InitializeAsync();

            test.Passed = true;
            test.Message = "Service initialized successfully";
        }
        catch (Exception ex)
        {
            test.Passed = false;
            test.Message = $"Service initialization failed: {ex.Message}";
            test.Exception = ex;
        }
        finally
        {
            test.EndTime = DateTime.Now;
            test.Duration = test.EndTime - test.StartTime;
            AddTestResult(test);
        }

        return test;
    }

    /// <summary>
    /// Tests device registration
    /// </summary>
    public async Task<NotificationTestResult> TestDeviceRegistrationAsync()
    {
        var test = new NotificationTestResult
        {
            TestName = "Device Registration",
            StartTime = DateTime.Now
        };

        try
        {
            var success = await this.pushNotificationService.RegisterAsync("test", "validation");

            test.Passed = success;
            test.Message = success
                ? "Device registered successfully"
                : "Device registration failed";

            var status = this.pushNotificationService.RegistrationStatus;
            test.Details = $"Registration status: {status.IsRegistered}, Token available: {!string.IsNullOrEmpty(status.Token)}";
        }
        catch (Exception ex)
        {
            test.Passed = false;
            test.Message = $"Registration error: {ex.Message}";
            test.Exception = ex;
        }
        finally
        {
            test.EndTime = DateTime.Now;
            test.Duration = test.EndTime - test.StartTime;
            AddTestResult(test);
        }

        return test;
    }

    /// <summary>
    /// Tests token validation
    /// </summary>
    public Task<NotificationTestResult> TestTokenValidationAsync()
    {
        var test = new NotificationTestResult
        {
            TestName = "Token Validation",
            StartTime = DateTime.Now
        };

        try
        {
            var deviceInstallation = this.pushNotificationService.GetDeviceInstallation();

            if (deviceInstallation != null)
            {
                var hasValidToken = !string.IsNullOrWhiteSpace(deviceInstallation.PushChannel);
                var hasValidId = !string.IsNullOrWhiteSpace(deviceInstallation.InstallationId);
                var hasValidPlatform = !string.IsNullOrWhiteSpace(deviceInstallation.Platform);

                test.Passed = hasValidToken && hasValidId && hasValidPlatform;
                test.Message = test.Passed
                    ? "Device installation is valid"
                    : "Device installation has missing or invalid data";

                test.Details = $"Token: {(hasValidToken ? "Valid" : "Invalid")}, " +
                              $"ID: {(hasValidId ? "Valid" : "Invalid")}, " +
                              $"Platform: {(hasValidPlatform ? "Valid" : "Invalid")}";
            }
            else
            {
                test.Passed = false;
                test.Message = "Device installation is null";
            }
        }
        catch (Exception ex)
        {
            test.Passed = false;
            test.Message = $"Token validation error: {ex.Message}";
            test.Exception = ex;
        }
        finally
        {
            test.EndTime = DateTime.Now;
            test.Duration = test.EndTime - test.StartTime;
            AddTestResult(test);
        }

        return Task.FromResult(test);
    }

    /// <summary>
    /// Tests sending a notification
    /// </summary>
    public async Task<NotificationTestResult> TestNotificationSendAsync()
    {
        var test = new NotificationTestResult
        {
            TestName = "Test Notification Send",
            StartTime = DateTime.Now
        };

        try
        {
            var request = new NotificationRequest
            {
                Text = "Test notification from validation suite",
                Action = "open_app",
                Silent = false
            };

            var success = await this.pushNotificationService.SendTestNotificationAsync(request);

            test.Passed = success;
            test.Message = success
                ? "Test notification sent successfully"
                : "Failed to send test notification";
        }
        catch (Exception ex)
        {
            test.Passed = false;
            test.Message = $"Send test error: {ex.Message}";
            test.Exception = ex;
        }
        finally
        {
            test.EndTime = DateTime.Now;
            test.Duration = test.EndTime - test.StartTime;
            AddTestResult(test);
        }

        return test;
    }

    /// <summary>
    /// Tests sending a silent notification
    /// </summary>
    public async Task<NotificationTestResult> TestSilentNotificationAsync()
    {
        var test = new NotificationTestResult
        {
            TestName = "Silent Notification Send",
            StartTime = DateTime.Now
        };

        try
        {
            var request = new NotificationRequest
            {
                Text = "Silent test notification",
                Action = "background_sync",
                Silent = true
            };

            var success = await this.pushNotificationService.SendTestNotificationAsync(request);

            test.Passed = success;
            test.Message = success
                ? "Silent notification sent successfully"
                : "Failed to send silent notification";
        }
        catch (Exception ex)
        {
            test.Passed = false;
            test.Message = $"Silent notification error: {ex.Message}";
            test.Exception = ex;
        }
        finally
        {
            test.EndTime = DateTime.Now;
            test.Duration = test.EndTime - test.StartTime;
            AddTestResult(test);
        }

        return test;
    }

    /// <summary>
    /// Validates notification payload format
    /// </summary>
    public NotificationTestResult ValidateNotificationPayload(string payload, string platform)
    {
        var test = new NotificationTestResult
        {
            TestName = $"Payload Validation ({platform})",
            StartTime = DateTime.Now
        };

        try
        {
            // Try to parse as JSON
            var jsonDoc = JsonDocument.Parse(payload);

            // Basic validation based on platform
            bool isValid = platform.ToLowerInvariant() switch
            {
                "android" or "fcm" => ValidateAndroidPayload(jsonDoc),
                "ios" or "apns" => ValidateIOSPayload(jsonDoc),
                _ => false
            };

            test.Passed = isValid;
            test.Message = isValid
                ? $"Valid {platform} payload format"
                : $"Invalid {platform} payload format";
        }
        catch (JsonException ex)
        {
            test.Passed = false;
            test.Message = $"Invalid JSON format: {ex.Message}";
            test.Exception = ex;
        }
        catch (Exception ex)
        {
            test.Passed = false;
            test.Message = $"Payload validation error: {ex.Message}";
            test.Exception = ex;
        }
        finally
        {
            test.EndTime = DateTime.Now;
            test.Duration = test.EndTime - test.StartTime;
            AddTestResult(test);
        }

        return test;
    }

    private bool ValidateAndroidPayload(JsonDocument jsonDoc)
    {
        var root = jsonDoc.RootElement;
        return root.TryGetProperty("message", out var message) &&
               (message.TryGetProperty("notification", out _) || message.TryGetProperty("data", out _));
    }

    private bool ValidateIOSPayload(JsonDocument jsonDoc)
    {
        var root = jsonDoc.RootElement;
        return root.TryGetProperty("aps", out _);
    }

    private void AddTestResult(NotificationTestResult result)
    {
        this.testResults.Add(result);
        TestCompleted?.Invoke(this, result);

        this.logger.LogInformation("Test completed: {TestName} - {Result}",
            result.TestName, result.Passed ? "PASSED" : "FAILED");
    }
}
