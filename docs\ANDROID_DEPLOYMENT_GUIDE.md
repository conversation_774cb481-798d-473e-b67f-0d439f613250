# Android Deployment Guide for Samsung Galaxy S10+

This comprehensive guide provides multiple deployment methods for installing the .NET MAUI Push Notification application on a Samsung Galaxy S10+ Android device, with emphasis on methods that don't require USB debugging.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Method 1: Wireless ADB Deployment](#method-1-wireless-adb-deployment)
3. [Method 2: APK Sideloading via OneDrive](#method-2-apk-sideloading-via-onedrive)
4. [Method 3: Google Play Store Deployment](#method-3-google-play-store-deployment)
5. [Method 4: Samsung Galaxy Store Deployment](#method-4-samsung-galaxy-store-deployment)
6. [Build Configuration](#build-configuration)
7. [Troubleshooting](#troubleshooting)

## Prerequisites

### Development Machine Requirements

- Windows 10/11 with .NET 10 SDK installed
- Visual Studio 2022 (17.12 or later) with MAUI workload
- Android SDK (API Level 21 or higher)
- Java Development Kit (JDK) 11 or later
- Azure CLI (for notification hub configuration)
- Git for version control

### Target Device Requirements (Samsung Galaxy S10+)

- Android 7.0 (API Level 24) or higher
- Minimum 2GB available storage space
- Active internet connection (WiFi or mobile data)
- Google Play Services installed and updated
- Samsung account (for Galaxy Store deployment)

### Network Requirements

- Both development machine and Samsung Galaxy S10+ on same WiFi network (for wireless deployment)
- OneDrive account with sufficient storage (for APK transfer method)
- Internet access for push notification services

## Method 1: Wireless ADB Deployment

This method allows deployment without USB cable using ADB over WiFi.

### Step 1: Enable Developer Options on Samsung Galaxy S10+

1. Open **Settings** → **About phone**
2. Tap **Build number** 7 times rapidly
3. Enter your device PIN/password when prompted
4. Developer options will be enabled

### Step 2: Configure Wireless Debugging

1. Go to **Settings** → **Developer options**
2. Enable **USB debugging**
3. Enable **Wireless debugging** (Android 11+) or **ADB over network**
4. Note the IP address and port displayed (e.g., *************:5555)

### Step 3: Connect from Development Machine

Open PowerShell/Command Prompt on your development machine:

```powershell
# Navigate to Android SDK platform-tools directory
cd "C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools"

# Connect to device wirelessly
adb connect *************:5555

# Verify connection
adb devices
```

### Step 4: Deploy Application

```powershell
# Navigate to project directory
cd "C:\dev\android\push-notification"

# Build and deploy in Release mode
dotnet build src/MauiFE/MauiFrontEnd.csproj -c Release -f net10.0-android
dotnet publish src/MauiFE/MauiFrontEnd.csproj -c Release -f net10.0-android

# Install APK to device
adb install -r "src\MauiFE\bin\Release\net10.0-android\publish\com.companyname.mauiapp10pushnotifications-Signed.apk"
```

## Method 2: APK Sideloading via OneDrive

This method transfers the APK file through OneDrive for manual installation.

### Step 1: Build Release APK

On your development machine:

```powershell
# Navigate to project directory
cd "C:\dev\android\push-notification"

# Clean previous builds
dotnet clean src/MauiFE/MauiFrontEnd.csproj

# Build release APK
dotnet publish src/MauiFE/MauiFrontEnd.csproj -c Release -f net10.0-android -p:AndroidPackageFormat=apk -p:AndroidKeyStore=true
```

### Step 2: Upload APK to OneDrive

1. Locate the generated APK file:
   - Path: `src\MauiFE\bin\Release\net10.0-android\publish\`
   - File: `com.companyname.mauiapp10pushnotifications-Signed.apk`

2. Upload the APK file to your OneDrive account
3. Share the file or note the location for device access

### Step 3: Download and Install on Samsung Galaxy S10+

1. **Enable Unknown Sources:**
   - Go to **Settings** → **Biometrics and security**
   - Enable **Install unknown apps**
   - Select **OneDrive** and enable **Allow from this source**

2. **Download APK:**
   - Open OneDrive app on your Samsung Galaxy S10+
   - Navigate to the uploaded APK file
   - Download the APK to your device

3. **Install APK:**
   - Open **Files** app or **Downloads**
   - Locate the downloaded APK file
   - Tap the APK file to begin installation
   - Follow the installation prompts
   - Grant necessary permissions when requested

### Step 4: Configure Push Notifications

After installation:

1. Open the **MauiApp10PushNotifications** app
2. Grant notification permissions when prompted
3. Navigate to the Push Notifications page
4. Tap **Register Device** to enable push notifications
5. Test notifications using the **Send Test Notification** feature

## Method 3: Google Play Store Deployment

For production deployment through Google Play Store.

### Step 1: Prepare for Play Store

1. **Create Google Play Console Account:**
   - Visit [Google Play Console](https://play.google.com/console)
   - Pay the one-time $25 registration fee
   - Complete developer profile

2. **Generate Upload Key:**
   ```powershell
   # Create upload keystore
   keytool -genkey -v -keystore upload-keystore.jks -keyalg RSA -keysize 2048 -validity 10000 -alias upload
   ```

3. **Configure App Signing:**
   - Update `MauiFrontEnd.csproj` with keystore information
   - Set signing configuration in project properties

### Step 2: Build App Bundle

```powershell
# Build Android App Bundle (AAB)
dotnet publish src/MauiFE/MauiFrontEnd.csproj -c Release -f net10.0-android -p:AndroidPackageFormat=aab
```

### Step 3: Upload to Play Store

1. Create new app in Google Play Console
2. Upload the generated AAB file
3. Complete store listing information
4. Set up content rating and pricing
5. Submit for review

### Step 4: Install from Play Store

Once approved:
1. Search for your app in Google Play Store on Samsung Galaxy S10+
2. Install normally through the store
3. Configure push notifications as described in Method 2, Step 4

## Method 4: Samsung Galaxy Store Deployment

Alternative app store for Samsung devices.

### Step 1: Samsung Developer Account

1. Create account at [Samsung Developers](https://developer.samsung.com)
2. Complete seller registration process
3. Access Galaxy Store Developer Portal

### Step 2: Prepare Application

1. Build signed APK using Method 2, Step 1
2. Prepare store assets (screenshots, descriptions, icons)
3. Complete Samsung-specific testing requirements

### Step 3: Submit to Galaxy Store

1. Upload APK to Galaxy Store Developer Portal
2. Complete app information and metadata
3. Submit for Samsung's review process
4. Monitor approval status

### Step 4: Install from Galaxy Store

1. Open **Galaxy Store** on Samsung Galaxy S10+
2. Search for your application
3. Install through Galaxy Store interface
4. Configure push notifications

## Build Configuration

### Release Build Settings

Ensure your `MauiFrontEnd.csproj` includes:

```xml
<PropertyGroup Condition="'$(Configuration)' == 'Release'">
    <AndroidPackageFormat>apk</AndroidPackageFormat>
    <AndroidUseAapt2>true</AndroidUseAapt2>
    <AndroidCreatePackagePerAbi>false</AndroidCreatePackagePerAbi>
    <AndroidKeyStore>true</AndroidKeyStore>
    <AndroidSigningKeyStore>path-to-your-keystore.jks</AndroidSigningKeyStore>
    <AndroidSigningKeyAlias>your-key-alias</AndroidSigningKeyAlias>
    <AndroidSigningKeyPass>your-key-password</AndroidSigningKeyPass>
    <AndroidSigningStorePass>your-store-password</AndroidSigningStorePass>
</PropertyGroup>
```

### Environment Configuration

Update `MauiProgram.cs` with production values:

```csharp
builder.Services.Configure<NotificationHubOptions>(options =>
{
    options.Name = "your-production-hub-name";
    options.ConnectionString = "your-production-connection-string";
});
```

## Troubleshooting

### Common Samsung Galaxy S10+ Issues

1. **Installation Blocked:**
   - Ensure "Install unknown apps" is enabled for the source
   - Check if Samsung Knox security is blocking installation
   - Try disabling Samsung's security scanning temporarily

2. **Push Notifications Not Working:**
   - Verify notification permissions are granted
   - Check if battery optimization is disabled for the app
   - Ensure Google Play Services are updated

3. **App Crashes on Launch:**
   - Check Android version compatibility
   - Verify all required permissions are granted
   - Review device logs using ADB logcat

### Network Connectivity Issues

1. **Wireless ADB Connection Fails:**
   - Ensure both devices are on the same network
   - Check firewall settings on development machine
   - Restart ADB server: `adb kill-server && adb start-server`

2. **OneDrive Download Issues:**
   - Check internet connectivity
   - Verify OneDrive app is updated
   - Try downloading through web browser instead

### Build and Deployment Errors

1. **Build Failures:**
   - Clean solution and rebuild
   - Update NuGet packages
   - Check .NET 10 SDK installation

2. **Signing Errors:**
   - Verify keystore path and passwords
   - Ensure keystore file exists and is accessible
   - Check key alias matches configuration

For additional support, refer to the [Troubleshooting Guide](TROUBLESHOOTING_GUIDE.md) or contact the development team.
